'use client';

import React from 'react';
import { RightDrawerProps } from '../alert-dashboard.types';

/**
 * RightDrawer Component
 * System overview drawer for the alert dashboard
 */
function RightDrawer({ isOpen = true, onToggle, className }: RightDrawerProps) {
    return (
        <div
            className={`bg-[#0d131ff2] transition-all duration-300 relative ${className || ''}`}
            style={{ gridArea: 'drawer' }}>
            {/* Toggle Button - shows when drawer is closed */}
            {!isOpen && (
                <div className="absolute right-0 top-1/5 transform -translate-y-1/2 -translate-x-full">
                    <button
                        onClick={onToggle}
                        className="bg-[#0d131ff2] border border-gray-600 text-gray-300 p-2 rounded-l hover:bg-gray-600 hover:text-white transition-all duration-200"
                        aria-label="Open drawer">
                        ⚙️
                    </button>
                </div>
            )}

            {/* Drawer Content - shows when drawer is open */}
            {isOpen && (
                <div className="h-full flex flex-col">
                    {/* Header */}
                    <div className="flex items-center justify-between p-4">
                        <h3 className="text-white text-lg font-semibold">System Overview</h3>
                        <button
                            onClick={onToggle}
                            className="bg-transparent border-none text-gray-300 text-xl cursor-pointer p-1 rounded hover:bg-gray-600 hover:text-white transition-all duration-200"
                            aria-label="Close drawer">
                            ✕
                        </button>
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-4 text-gray-200 overflow-y-auto"></div>
                </div>
            )}
        </div>
    );
}

export default RightDrawer;
