'use client';

import React from 'react';
import { SystemCard } from '@/components/features/alert-dashboard/layout';

export default function SystemCardsDemoPage() {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">System Cards Demo</h1>
        <p className="text-gray-600 mb-8">
          Demonstration of the SystemCard component with different system types and states.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Fire Alarm System */}
          <SystemCard
            title="Fire Alarm System"
            icon="🔥"
            primaryMetric={{
              label: "Total Devices",
              value: 42
            }}
            secondaryMetrics={[
              {
                label: "Active Alarms",
                value: 5,
                isAlert: true
              }
            ]}
            onCardClick={() => alert('Fire Alarm System clicked!')}
          />

          {/* Access Control System */}
          <SystemCard
            title="Access Control"
            icon="🚪"
            primaryMetric={{
              label: "Total Doors",
              value: 28
            }}
            secondaryMetrics={[
              {
                label: "Open",
                value: 12,
                highlight: true
              },
              {
                label: "Closed",
                value: 16
              }
            ]}
            onCardClick={() => alert('Access Control System clicked!')}
          />

          {/* CCTV System */}
          <SystemCard
            title="CCTV System"
            icon="📹"
            primaryMetric={{
              label: "Total Cameras",
              value: 156
            }}
            secondaryMetrics={[
              {
                label: "Active Incidents",
                value: 2,
                isAlert: true
              }
            ]}
            onCardClick={() => alert('CCTV System clicked!')}
          />

          {/* Gate Barriers System */}
          <SystemCard
            title="Gate Barriers"
            icon="🚧"
            primaryMetric={{
              label: "Total Barriers",
              value: 8
            }}
            secondaryMetrics={[
              {
                label: "Unauthorized Attempts",
                value: 3,
                isAlert: true
              }
            ]}
            onCardClick={() => alert('Gate Barriers System clicked!')}
          />

          {/* Loading State Example */}
          <SystemCard
            title="Loading System"
            icon="⏳"
            primaryMetric={{
              label: "Loading...",
              value: 0
            }}
            secondaryMetrics={[
              {
                label: "Please wait",
                value: "..."
              }
            ]}
            isLoading={true}
          />

          {/* System without alerts */}
          <SystemCard
            title="Normal System"
            icon="✅"
            primaryMetric={{
              label: "Total Items",
              value: 100
            }}
            secondaryMetrics={[
              {
                label: "Active",
                value: 95,
                highlight: true
              },
              {
                label: "Inactive",
                value: 5
              }
            ]}
          />
        </div>

        <div className="mt-12 bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">How to Use</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <p>
              <strong>Storybook:</strong> Run <code className="bg-gray-100 px-2 py-1 rounded">npm run storybook</code> 
              and navigate to "Features/Alert Dashboard/SystemCard" to see all component variations.
            </p>
            <p>
              <strong>Interactive:</strong> Click on any card above to see the click handler in action.
            </p>
            <p>
              <strong>States:</strong> The component supports loading states, alert highlighting, and custom styling.
            </p>
            <p>
              <strong>Icons:</strong> Supports both emoji strings and React component icons.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
