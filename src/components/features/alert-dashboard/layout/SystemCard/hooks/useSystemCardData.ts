import { useState, useEffect } from 'react';
import { SystemMetric } from '../SystemCard';

// Static data for demonstration (will be replaced with actual store later)
const STATIC_SYSTEM_DATA = {
  fireAlarm: {
    title: 'Fire Alarm System',
    icon: '🔥',
    primaryMetric: {
      label: 'Total Devices',
      value: 42
    },
    secondaryMetrics: [
      {
        label: 'Active Alarms',
        value: 5,
        isAlert: true
      }
    ]
  },
  accessControl: {
    title: 'Access Control',
    icon: '🚪',
    primaryMetric: {
      label: 'Total Doors',
      value: 28
    },
    secondaryMetrics: [
      {
        label: 'Open',
        value: 12,
        highlight: true
      },
      {
        label: 'Closed',
        value: 16
      }
    ]
  },
  cctv: {
    title: 'CCTV System',
    icon: '📹',
    primaryMetric: {
      label: 'Total Cameras',
      value: 156
    },
    secondaryMetrics: [
      {
        label: 'Active Incidents',
        value: 2,
        isAlert: true
      }
    ]
  },
  gateBarriers: {
    title: 'Gate Barriers',
    icon: '🚧',
    primaryMetric: {
      label: 'Total Barriers',
      value: 8
    },
    secondaryMetrics: [
      {
        label: 'Unauthorized Attempts',
        value: 3,
        isAlert: true
      }
    ]
  }
};

export type SystemType = keyof typeof STATIC_SYSTEM_DATA;

interface UseSystemCardDataReturn {
  title: string;
  icon: string;
  primaryMetric: SystemMetric;
  secondaryMetrics: SystemMetric[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useSystemCardData = (systemType: SystemType): UseSystemCardDataReturn => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate loading delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [systemType]);

  const refetch = () => {
    setIsLoading(true);
    setError(null);
    
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const systemData = STATIC_SYSTEM_DATA[systemType];

  return {
    ...systemData,
    isLoading,
    error,
    refetch
  };
};

// Hook for getting all systems data
export const useAllSystemsData = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const refetch = () => {
    setIsLoading(true);
    setError(null);
    
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  return {
    systems: Object.entries(STATIC_SYSTEM_DATA).map(([key, data]) => ({
      id: key as SystemType,
      ...data
    })),
    isLoading,
    error,
    refetch
  };
};
