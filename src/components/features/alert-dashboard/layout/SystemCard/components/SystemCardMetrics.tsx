import React from 'react';
import { cn } from '@/shared/utils';
import { SystemMetric } from '../SystemCard';

interface SystemCardMetricsProps {
    primaryMetric: SystemMetric;
    secondaryMetrics: SystemMetric[];
    className?: string;
}

const SystemCardMetrics: React.FC<SystemCardMetricsProps> = ({ primaryMetric, secondaryMetrics, className }) => {
    return (
        <div className={cn('space-y-4', className)}>
            {/* Primary Metric */}
            <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{primaryMetric.label}</span>
                <span
                    className={cn(
                        'text-2xl font-bold',
                        primaryMetric.isAlert ? 'text-red-600' : 'text-gray-900',
                        primaryMetric.highlight && 'text-blue-600',
                    )}>
                    {primaryMetric.value}
                </span>
            </div>

            {/* Secondary Metrics */}
            {secondaryMetrics.length > 0 && (
                <div className="space-y-2">
                    {secondaryMetrics.map((metric, index) => (
                        <div key={index} className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">{metric.label}</span>
                            <div className="flex items-center gap-2">
                                <span
                                    className={cn(
                                        'text-sm font-medium',
                                        metric.isAlert ? 'text-red-600' : 'text-gray-900',
                                        metric.highlight && 'text-blue-600',
                                    )}>
                                    {metric.value}
                                </span>
                                {/* Placeholder for AlertBadge - will be implemented later */}
                                {metric.isAlert && (
                                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default SystemCardMetrics;
