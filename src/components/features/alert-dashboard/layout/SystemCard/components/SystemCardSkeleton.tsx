import React from 'react';
import { cn } from '@/shared/utils';

interface SystemCardSkeletonProps {
    secondaryMetricsCount?: number;
    className?: string;
}

const SystemCardSkeleton: React.FC<SystemCardSkeletonProps> = ({ secondaryMetricsCount = 2, className }) => {
    return (
        <div className={cn('bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse', className)}>
            {/* Header Skeleton */}
            <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="h-5 bg-gray-200 rounded w-32"></div>
            </div>

            {/* Primary Metric Skeleton */}
            <div className="mb-4">
                <div className="flex justify-between items-center">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-6 bg-gray-200 rounded w-8"></div>
                </div>
            </div>

            {/* Secondary Metrics Skeleton */}
            <div className="space-y-3">
                {Array.from({ length: secondaryMetricsCount }).map((_, index) => (
                    <div key={index} className="flex justify-between items-center">
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                        <div className="h-4 bg-gray-200 rounded w-6"></div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default SystemCardSkeleton;
