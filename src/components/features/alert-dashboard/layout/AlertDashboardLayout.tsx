'use client';

import React, { useState } from 'react';
import { AlertDashboardLayoutProps } from '../alert-dashboard.types';
import LeftSidebar from './LeftSidebar';
import TopBar from './TopBar';
import RightDrawer from './RightDrawer';
import ContentBody from './ContentBody';

/**
 * AlertDashboardLayout Component
 * Main layout container for the alert dashboard
 */
function AlertDashboardLayout({
    children,
    className,
    rightDrawerOpen = true,
    onRightDrawerToggle,
}: AlertDashboardLayoutProps) {
    const [drawerOpen, setDrawerOpen] = useState(rightDrawerOpen);

    // Handle drawer toggle
    const handleDrawerToggle = () => {
        const newOpen = !drawerOpen;
        setDrawerOpen(newOpen);
        onRightDrawerToggle?.();
    };

    // Generate dynamic grid template columns based on state
    const getGridTemplateColumns = () => {
        const sidebarWidth = '80px';
        const drawerWidth = drawerOpen ? '352px' : '0px';
        return `${sidebarWidth} 1fr ${drawerWidth}`;
    };

    return (
        <div
            className={`grid h-screen overflow-hidden bg-gray-900 grid-rows-[80px_1fr] ${className || ''}`}
            style={{
                gridTemplateColumns: getGridTemplateColumns(),
                gridTemplateAreas: `
                    'sidebar topbar drawer'
                    'sidebar content drawer'
                `,
            }}>
            {/* Left Sidebar */}
            <LeftSidebar />

            {/* Top Bar */}
            <TopBar />

            {/* Main Content Area */}
            <ContentBody>{children}</ContentBody>

            {/* Right Drawer */}
            <RightDrawer isOpen={drawerOpen} onToggle={handleDrawerToggle} />
        </div>
    );
}

export default AlertDashboardLayout;
