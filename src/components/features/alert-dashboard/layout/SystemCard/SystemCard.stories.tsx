import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import SystemCard from './SystemCard';

// Mock icons for demonstration
const FireIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C8.5 2 6 4.5 6 8c0 2.5 1.5 4.5 3 6 1 1 2 2 2 3 0 .5-.5 1-1 1s-1-.5-1-1c0-.5-.5-1-1-1s-1 .5-1 1c0 1.5 1.5 3 3 3s3-1.5 3-3c0-1-1-2-2-3-1.5-1.5-3-3.5-3-6 0-2.5 1.5-4 4-4s4 1.5 4 4c0 .5.5 1 1 1s1-.5 1-1c0-3.5-2.5-6-6-6z"/>
  </svg>
);

const DoorIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M8 3v18h2V3H8zm6 0v18h2V3h-2zM4 21h16v-2H4v2zM4 5h16V3H4v2z"/>
  </svg>
);

const CameraIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 9c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3zM18 4h-3l-2-2H9L7 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"/>
  </svg>
);

const BarrierIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
  </svg>
);

const meta: Meta<typeof SystemCard> = {
  title: 'Features/Alert Dashboard/SystemCard',
  component: SystemCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A reusable system card component that displays system information with metrics and alerts.'
      }
    }
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'The title of the system card'
    },
    icon: {
      control: false,
      description: 'Icon component or emoji string'
    },
    isLoading: {
      control: 'boolean',
      description: 'Shows loading skeleton when true'
    },
    onCardClick: {
      action: 'clicked',
      description: 'Callback function when card is clicked'
    }
  }
};

export default meta;
type Story = StoryObj<typeof SystemCard>;

// Fire Alarm System Story
export const FireAlarmSystem: Story = {
  args: {
    title: 'Fire Alarm System',
    icon: FireIcon,
    primaryMetric: {
      label: 'Total Devices',
      value: 42
    },
    secondaryMetrics: [
      {
        label: 'Active Alarms',
        value: 5,
        isAlert: true
      }
    ]
  }
};

// Access Control System Story
export const AccessControlSystem: Story = {
  args: {
    title: 'Access Control',
    icon: DoorIcon,
    primaryMetric: {
      label: 'Total Doors',
      value: 28
    },
    secondaryMetrics: [
      {
        label: 'Open',
        value: 12,
        highlight: true
      },
      {
        label: 'Closed',
        value: 16
      }
    ]
  }
};

// CCTV System Story
export const CCTVSystem: Story = {
  args: {
    title: 'CCTV System',
    icon: CameraIcon,
    primaryMetric: {
      label: 'Total Cameras',
      value: 156
    },
    secondaryMetrics: [
      {
        label: 'Active Incidents',
        value: 2,
        isAlert: true
      }
    ]
  }
};

// Gate Barriers System Story
export const GateBarriersSystem: Story = {
  args: {
    title: 'Gate Barriers',
    icon: BarrierIcon,
    primaryMetric: {
      label: 'Total Barriers',
      value: 8
    },
    secondaryMetrics: [
      {
        label: 'Unauthorized Attempts',
        value: 3,
        isAlert: true
      }
    ]
  }
};

// With Emoji Icons
export const WithEmojiIcons: Story = {
  args: {
    title: 'Fire Alarm System',
    icon: '🔥',
    primaryMetric: {
      label: 'Total Devices',
      value: 42
    },
    secondaryMetrics: [
      {
        label: 'Active Alarms',
        value: 5,
        isAlert: true
      }
    ]
  }
};

// Loading State
export const LoadingState: Story = {
  args: {
    title: 'Fire Alarm System',
    icon: FireIcon,
    primaryMetric: {
      label: 'Total Devices',
      value: 42
    },
    secondaryMetrics: [
      {
        label: 'Active Alarms',
        value: 5,
        isAlert: true
      }
    ],
    isLoading: true
  }
};

// Interactive Card
export const InteractiveCard: Story = {
  args: {
    title: 'Fire Alarm System',
    icon: FireIcon,
    primaryMetric: {
      label: 'Total Devices',
      value: 42
    },
    secondaryMetrics: [
      {
        label: 'Active Alarms',
        value: 5,
        isAlert: true
      }
    ],
    onCardClick: () => alert('Card clicked!')
  }
};

// All Systems Grid
export const AllSystemsGrid: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl">
      <SystemCard
        title="Fire Alarm System"
        icon="🔥"
        primaryMetric={{ label: 'Total Devices', value: 42 }}
        secondaryMetrics={[{ label: 'Active Alarms', value: 5, isAlert: true }]}
      />
      <SystemCard
        title="Access Control"
        icon="🚪"
        primaryMetric={{ label: 'Total Doors', value: 28 }}
        secondaryMetrics={[
          { label: 'Open', value: 12, highlight: true },
          { label: 'Closed', value: 16 }
        ]}
      />
      <SystemCard
        title="CCTV System"
        icon="📹"
        primaryMetric={{ label: 'Total Cameras', value: 156 }}
        secondaryMetrics={[{ label: 'Active Incidents', value: 2, isAlert: true }]}
      />
      <SystemCard
        title="Gate Barriers"
        icon="🚧"
        primaryMetric={{ label: 'Total Barriers', value: 8 }}
        secondaryMetrics={[{ label: 'Unauthorized Attempts', value: 3, isAlert: true }]}
      />
    </div>
  )
};
