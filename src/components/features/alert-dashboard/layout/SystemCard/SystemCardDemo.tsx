import React from 'react';
import SystemCard from './SystemCard';

// Demo component to show how to use SystemCard
const SystemCardDemo: React.FC = () => {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">System Cards Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-6xl">
        {/* Fire Alarm System */}
        <SystemCard
          title="Fire Alarm System"
          icon="🔥"
          primaryMetric={{
            label: "Total Devices",
            value: 42
          }}
          secondaryMetrics={[
            {
              label: "Active Alarms",
              value: 5,
              isAlert: true
            }
          ]}
          onCardClick={() => alert('Fire Alarm System clicked!')}
        />

        {/* Access Control System */}
        <SystemCard
          title="Access Control"
          icon="🚪"
          primaryMetric={{
            label: "Total Doors",
            value: 28
          }}
          secondaryMetrics={[
            {
              label: "Open",
              value: 12,
              highlight: true
            },
            {
              label: "Closed",
              value: 16
            }
          ]}
          onCardClick={() => alert('Access Control System clicked!')}
        />

        {/* CCTV System */}
        <SystemCard
          title="CCTV System"
          icon="📹"
          primaryMetric={{
            label: "Total Cameras",
            value: 156
          }}
          secondaryMetrics={[
            {
              label: "Active Incidents",
              value: 2,
              isAlert: true
            }
          ]}
          onCardClick={() => alert('CCTV System clicked!')}
        />

        {/* Gate Barriers System */}
        <SystemCard
          title="Gate Barriers"
          icon="🚧"
          primaryMetric={{
            label: "Total Barriers",
            value: 8
          }}
          secondaryMetrics={[
            {
              label: "Unauthorized Attempts",
              value: 3,
              isAlert: true
            }
          ]}
          onCardClick={() => alert('Gate Barriers System clicked!')}
        />

        {/* Loading State Example */}
        <SystemCard
          title="Loading System"
          icon="⏳"
          primaryMetric={{
            label: "Loading...",
            value: 0
          }}
          secondaryMetrics={[]}
          isLoading={true}
        />

        {/* System without alerts */}
        <SystemCard
          title="Normal System"
          icon="✅"
          primaryMetric={{
            label: "Total Items",
            value: 100
          }}
          secondaryMetrics={[
            {
              label: "Active",
              value: 95,
              highlight: true
            },
            {
              label: "Inactive",
              value: 5
            }
          ]}
        />
      </div>
    </div>
  );
};

export default SystemCardDemo;
