# Task 004.5: SystemOverview Container Component

## Task Overview

**Component**: SystemOverview (Container Component)
**Priority**: Medium
**Developer Assignment**: Developer
**Dependencies**: 
- SystemCard (Task 004.4)
- useSystemStore (Task 004.3)
**Location**: `src/components/features/dashboard/layout/SystemOverview/`

## Component Description

Create the main container component that orchestrates multiple SystemCard instances in a responsive grid layout. This component serves as the right drawer content, managing data flow from the Zustand store to individual system cards.

## UI Drawing

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ SystemOverview Container - Right Drawer Layout                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Desktop Layout (2x2 Grid):                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────────┐  ┌─────────────────────────┐                │ │
│ │ │     Fire Alarm          │  │   Access Control        │                │ │
│ │ │   Total Devices: 42     │  │   Total Doors: 28       │                │ │
│ │ │   Active Alarms: 5 🔴   │  │   Open: 12              │                │ │
│ │ └─────────────────────────┘  └─────────────────────────┘                │ │
│ │                                                                         │ │
│ │ ┌─────────────────────────┐  ┌─────────────────────────┐                │ │
│ │ │       CCTV              │  │    Gate Barriers        │                │ │
│ │ │   Total Cameras: 156    │  │   Total Barriers: 8     │                │ │
│ │ │   Active Incidents: 2🔴 │  │   Unauthorized: 3 🔴    │                │ │
│ │ └─────────────────────────┘  └─────────────────────────┘                │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Mobile Layout (Stacked):                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                    Fire Alarm                                       │ │ │
│ │ │                 Total Devices: 42                                   │ │ │
│ │ │                 Active Alarms: 5 🔴                                 │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                  Access Control                                     │ │ │
│ │ │                 Total Doors: 28                                     │ │ │
│ │ │                 Open: 12, Closed: 16                               │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                     CCTV                                            │ │ │
│ │ │                 Total Cameras: 156                                  │ │ │
│ │ │                 Active Incidents: 2 🔴                              │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────────────────────┐ │ │
│ │ │                  Gate Barriers                                      │ │ │
│ │ │                 Total Barriers: 8                                   │ │ │
│ │ │                 Unauthorized: 3 🔴                                  │ │ │
│ │ └─────────────────────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ Loading State:                                                              │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────────┐  ┌─────────────────────────┐                │ │
│ │ │ ████████████████████████ │  │ ████████████████████████ │                │ │
│ │ │ ████████████████████████ │  │ ████████████████████████ │                │ │
│ │ │ ████████████████████████ │  │ ████████████████████████ │                │ │
│ │ └─────────────────────────┘  └─────────────────────────┘                │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Props Structure

```typescript
interface SystemOverviewProps {
  className?: string;
  onSystemClick?: (systemType: SystemType) => void;
  showLoadingState?: boolean;
  enableAutoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  testId?: string;
}

type SystemType = 'fireAlarm' | 'accessControl' | 'cctv' | 'gateBarriers';
```

## System Configuration Mapping

```typescript
const SYSTEM_CONFIGS = {
  fireAlarm: {
    title: "Fire Alarm System",
    icon: FireIcon,
    dataSelector: (state: SystemState) => state.fireAlarm,
    getPrimaryMetric: (data) => ({
      label: "Total Devices",
      value: data.totalDevices
    }),
    getSecondaryMetrics: (data) => [
      {
        label: "Active Alarms",
        value: data.activeAlarms,
        isAlert: data.activeAlarms > 0
      }
    ]
  },
  
  accessControl: {
    title: "Access Control",
    icon: DoorIcon,
    dataSelector: (state: SystemState) => state.accessControl,
    getPrimaryMetric: (data) => ({
      label: "Total Doors",
      value: data.totalDoors
    }),
    getSecondaryMetrics: (data) => [
      {
        label: "Open",
        value: data.open,
        highlight: true
      },
      {
        label: "Closed",
        value: data.closed
      }
    ]
  },
  
  cctv: {
    title: "CCTV System",
    icon: CameraIcon,
    dataSelector: (state: SystemState) => state.cctv,
    getPrimaryMetric: (data) => ({
      label: "Total Cameras",
      value: data.totalCameras
    }),
    getSecondaryMetrics: (data) => [
      {
        label: "Active Incidents",
        value: data.activeIncidents,
        isAlert: data.activeIncidents > 0
      }
    ]
  },
  
  gateBarriers: {
    title: "Gate Barriers",
    icon: BarrierIcon,
    dataSelector: (state: SystemState) => state.gateBarriers,
    getPrimaryMetric: (data) => ({
      label: "Total Barriers",
      value: data.totalBarriers
    }),
    getSecondaryMetrics: (data) => [
      {
        label: "Unauthorized Attempts",
        value: data.unauthorizedAttempts,
        isAlert: data.unauthorizedAttempts > 0
      }
    ]
  }
};
```

## Responsibilities

- **Data Orchestration**: Connects Zustand store data to SystemCard components
- **Layout Management**: Responsive grid layout (2x2 desktop, stacked mobile)
- **Loading States**: Coordinates loading states across all system cards
- **Error Handling**: Manages error states and retry mechanisms
- **Auto Refresh**: Optional automatic data refresh functionality
- **Navigation**: Handles system card click events for navigation
- **Performance**: Optimizes re-renders through selective subscriptions

## Implementation Requirements

### Layout Specifications
- **Desktop Grid**: 2x2 grid with consistent gaps (1rem)
- **Mobile Stack**: Single column with vertical spacing
- **Responsive Breakpoints**: Tablet (2x1), Mobile (1x4)
- **Container Padding**: Consistent padding around the grid
- **Card Spacing**: Uniform gaps between cards

### Data Management
- **Store Integration**: Subscribe to useSystemStore for all system data
- **Selective Updates**: Only re-render cards when their data changes
- **Loading Coordination**: Show loading state until all systems load
- **Error Recovery**: Retry failed data fetches automatically
- **Cache Management**: Respect store caching and update policies

### Performance Optimization
- **Memoization**: React.memo for SystemCard instances
- **Selective Subscriptions**: Subscribe only to needed store slices
- **Batch Updates**: Group multiple store updates to reduce renders
- **Virtual Scrolling**: Consider for future expansion with many systems

### Auto Refresh Feature
- **Configurable Interval**: Default 30 seconds, configurable via props
- **Pause on Focus Loss**: Stop refreshing when tab is not active
- **Manual Refresh**: Support for manual refresh trigger
- **Connection Awareness**: Adjust refresh based on connection status

## File Structure

```
src/components/features/dashboard/layout/SystemOverview/
├── index.ts
├── SystemOverview.tsx
├── SystemOverview.test.tsx
├── SystemOverview.stories.tsx
├── hooks/
│   ├── useSystemOverviewData.ts
│   └── useAutoRefresh.ts
├── config/
│   └── systemConfigs.ts
└── components/
    ├── SystemGrid.tsx
    └── SystemOverviewSkeleton.tsx
```

## Testing Requirements

- **Unit Tests**: Data mapping, layout rendering, error handling
- **Integration Tests**: Store integration, SystemCard rendering
- **Responsive Tests**: Layout behavior at different screen sizes
- **Performance Tests**: Re-render frequency, memory usage
- **Auto Refresh Tests**: Interval behavior, pause/resume functionality
- **Error Handling Tests**: Network failures, partial data loading

## Success Criteria

- [ ] Renders all four system cards in proper grid layout
- [ ] Responsive design works across desktop, tablet, and mobile
- [ ] Proper integration with useSystemStore for data management
- [ ] Loading states coordinate across all system cards
- [ ] Error handling and retry mechanisms work correctly
- [ ] Auto refresh functionality works with configurable intervals
- [ ] Click handlers properly navigate to system detail pages
- [ ] Performance optimizations prevent unnecessary re-renders
- [ ] Accessibility standards met for container and navigation
- [ ] Complete test coverage including edge cases and error states

## Usage Examples

```tsx
// Basic usage in right drawer
<SystemOverview />

// With navigation handling
<SystemOverview 
  onSystemClick={(systemType) => {
    navigate(`/dashboard/${systemType}`);
  }}
/>

// With custom refresh interval
<SystemOverview 
  enableAutoRefresh={true}
  refreshInterval={60000} // 1 minute
/>

// With custom styling
<SystemOverview 
  className="custom-overview-styles"
  testId="system-overview-container"
/>
```

## Integration with Right Drawer

```tsx
// Integration in DashboardLayout
const RightDrawer = () => {
  return (
    <div className="right-drawer">
      <div className="drawer-header">
        <h2>System Overview</h2>
      </div>
      <div className="drawer-content">
        <SystemOverview 
          onSystemClick={handleSystemNavigation}
          enableAutoRefresh={true}
        />
      </div>
    </div>
  );
};
```

## Performance Considerations

- **Selective Subscriptions**: Each SystemCard subscribes only to its system data
- **Memoized Configs**: System configurations are memoized to prevent recreations
- **Efficient Updates**: Store updates trigger only affected card re-renders
- **Lazy Loading**: Consider lazy loading for system detail components

## Future Enhancements

- **Drag & Drop**: Reorderable system cards based on user preference
- **Filtering**: Show/hide specific system types based on user role
- **Customization**: User-configurable metrics and display options
- **Export**: Export system overview data to PDF or Excel
- **Alerts Summary**: Dedicated section for critical alerts across all systems