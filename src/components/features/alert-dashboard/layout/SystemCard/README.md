# SystemCard Component

A reusable, generic card component for displaying system information with metrics and alerts.

## Features

- ✅ Generic and reusable for any system type
- ✅ Support for both React component and emoji/string icons
- ✅ Primary and secondary metrics display
- ✅ Alert highlighting for critical values
- ✅ Loading states with skeleton animation
- ✅ Interactive click handlers
- ✅ Accessibility support (ARIA labels, keyboard navigation)
- ✅ Responsive design
- ✅ TypeScript support

## Usage

### Basic Usage

```tsx
import SystemCard from './SystemCard';

<SystemCard
  title="Fire Alarm System"
  icon="🔥"
  primaryMetric={{
    label: "Total Devices",
    value: 42
  }}
  secondaryMetrics={[
    {
      label: "Active Alarms",
      value: 5,
      isAlert: true
    }
  ]}
/>
```

### With React Icon Component

```tsx
import { FireIcon } from '@heroicons/react/24/outline';

<SystemCard
  title="Fire Alarm System"
  icon={FireIcon}
  primaryMetric={{
    label: "Total Devices",
    value: 42
  }}
  secondaryMetrics={[
    {
      label: "Active Alarms",
      value: 5,
      isAlert: true
    }
  ]}
/>
```

### Interactive Card

```tsx
<SystemCard
  title="Fire Alarm System"
  icon="🔥"
  primaryMetric={{
    label: "Total Devices",
    value: 42
  }}
  secondaryMetrics={[
    {
      label: "Active Alarms",
      value: 5,
      isAlert: true
    }
  ]}
  onCardClick={() => navigate('/fire-alarm')}
/>
```

### Loading State

```tsx
<SystemCard
  title="Fire Alarm System"
  icon="🔥"
  primaryMetric={{
    label: "Total Devices",
    value: 42
  }}
  secondaryMetrics={[
    {
      label: "Active Alarms",
      value: 5,
      isAlert: true
    }
  ]}
  isLoading={true}
/>
```

## Props

### SystemCardProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | The title of the system card |
| `icon` | `React.ComponentType<{ className?: string }> \| string` | - | Icon component or emoji/string |
| `primaryMetric` | `SystemMetric` | - | Main metric to display prominently |
| `secondaryMetrics` | `SystemMetric[]` | - | Array of additional metrics |
| `isLoading` | `boolean` | `false` | Shows loading skeleton when true |
| `className` | `string` | - | Additional CSS classes |
| `onCardClick` | `() => void` | - | Click handler for the card |
| `testId` | `string` | `'system-card'` | Test ID for testing |

### SystemMetric

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label for the metric |
| `value` | `number \| string` | - | Value of the metric |
| `isAlert` | `boolean` | `false` | Highlights metric in red with pulse animation |
| `highlight` | `boolean` | `false` | Highlights metric in blue |

## System Examples

### Fire Alarm System
```tsx
{
  title: "Fire Alarm System",
  icon: "🔥",
  primaryMetric: { label: "Total Devices", value: 42 },
  secondaryMetrics: [
    { label: "Active Alarms", value: 5, isAlert: true }
  ]
}
```

### Access Control System
```tsx
{
  title: "Access Control",
  icon: "🚪",
  primaryMetric: { label: "Total Doors", value: 28 },
  secondaryMetrics: [
    { label: "Open", value: 12, highlight: true },
    { label: "Closed", value: 16 }
  ]
}
```

### CCTV System
```tsx
{
  title: "CCTV System",
  icon: "📹",
  primaryMetric: { label: "Total Cameras", value: 156 },
  secondaryMetrics: [
    { label: "Active Incidents", value: 2, isAlert: true }
  ]
}
```

### Gate Barriers System
```tsx
{
  title: "Gate Barriers",
  icon: "🚧",
  primaryMetric: { label: "Total Barriers", value: 8 },
  secondaryMetrics: [
    { label: "Unauthorized Attempts", value: 3, isAlert: true }
  ]
}
```

## Storybook

View all component variations in Storybook:

```bash
npm run storybook
```

Navigate to: `Features/Alert Dashboard/SystemCard`

## Testing

Run tests:

```bash
npm test SystemCard
```

## File Structure

```
SystemCard/
├── index.ts                    # Main exports
├── SystemCard.tsx              # Main component
├── SystemCard.test.tsx         # Unit tests
├── SystemCard.stories.tsx      # Storybook stories
├── SystemCardDemo.tsx          # Demo component
├── README.md                   # This file
├── components/
│   ├── SystemCardHeader.tsx    # Header sub-component
│   ├── SystemCardMetrics.tsx   # Metrics sub-component
│   └── SystemCardSkeleton.tsx  # Loading skeleton
└── hooks/
    └── useSystemCardData.ts    # Data hook (static data for now)
```

## Future Enhancements

- Integration with AlertBadge component (Task 004.1)
- Integration with MetricDisplay component (Task 004.2)
- Integration with useSystemStore (Task 004.3)
- Chart integration for mini sparklines
- Status indicators (green/yellow/red)
- Expandable cards with additional details
