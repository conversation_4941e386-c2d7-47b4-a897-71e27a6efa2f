import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SystemCard, { SystemCardProps } from './SystemCard';

// Mock icon component
const MockIcon: React.FC<{ className?: string }> = ({ className }) => (
  <div className={className} data-testid="mock-icon">Icon</div>
);

const defaultProps: SystemCardProps = {
  title: 'Test System',
  icon: MockIcon,
  primaryMetric: {
    label: 'Total Devices',
    value: 42
  },
  secondaryMetrics: [
    {
      label: 'Active Alarms',
      value: 5,
      isAlert: true
    },
    {
      label: 'Normal Status',
      value: 37
    }
  ]
};

describe('SystemCard', () => {
  it('renders correctly with basic props', () => {
    render(<SystemCard {...defaultProps} />);
    
    expect(screen.getByText('Test System')).toBeInTheDocument();
    expect(screen.getByText('Total Devices')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('Active Alarms')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('Normal Status')).toBeInTheDocument();
    expect(screen.getByText('37')).toBeInTheDocument();
  });

  it('renders string icon correctly', () => {
    const propsWithStringIcon = {
      ...defaultProps,
      icon: '🔥'
    };
    
    render(<SystemCard {...propsWithStringIcon} />);
    expect(screen.getByText('🔥')).toBeInTheDocument();
  });

  it('renders component icon correctly', () => {
    render(<SystemCard {...defaultProps} />);
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('shows loading state correctly', () => {
    render(<SystemCard {...defaultProps} isLoading={true} />);
    
    expect(screen.getByTestId('system-card-loading')).toBeInTheDocument();
    expect(screen.queryByText('Test System')).not.toBeInTheDocument();
  });

  it('handles click events when onCardClick is provided', () => {
    const mockClick = jest.fn();
    render(<SystemCard {...defaultProps} onCardClick={mockClick} />);
    
    const card = screen.getByTestId('system-card');
    fireEvent.click(card);
    
    expect(mockClick).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard events when onCardClick is provided', () => {
    const mockClick = jest.fn();
    render(<SystemCard {...defaultProps} onCardClick={mockClick} />);
    
    const card = screen.getByTestId('system-card');
    fireEvent.keyDown(card, { key: 'Enter' });
    
    expect(mockClick).toHaveBeenCalledTimes(1);
  });

  it('applies alert styling for alert metrics', () => {
    render(<SystemCard {...defaultProps} />);
    
    const alertValue = screen.getByText('5');
    expect(alertValue).toHaveClass('text-red-600');
  });

  it('applies highlight styling for highlighted metrics', () => {
    const propsWithHighlight = {
      ...defaultProps,
      primaryMetric: {
        ...defaultProps.primaryMetric,
        highlight: true
      }
    };
    
    render(<SystemCard {...propsWithHighlight} />);
    
    const highlightedValue = screen.getByText('42');
    expect(highlightedValue).toHaveClass('text-blue-600');
  });

  it('renders without secondary metrics', () => {
    const propsWithoutSecondary = {
      ...defaultProps,
      secondaryMetrics: []
    };
    
    render(<SystemCard {...propsWithoutSecondary} />);
    
    expect(screen.getByText('Test System')).toBeInTheDocument();
    expect(screen.getByText('Total Devices')).toBeInTheDocument();
    expect(screen.queryByText('Active Alarms')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<SystemCard {...defaultProps} className="custom-class" />);
    
    const card = screen.getByTestId('system-card');
    expect(card).toHaveClass('custom-class');
  });

  it('uses custom testId', () => {
    render(<SystemCard {...defaultProps} testId="custom-test-id" />);
    
    expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
  });
});
