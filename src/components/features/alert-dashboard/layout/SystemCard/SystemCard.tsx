import React from 'react';
import { cn } from '@/shared/utils';

// Types and Interfaces
export interface SystemMetric {
    label: string;
    value: number | string;
    isAlert?: boolean;
    highlight?: boolean;
}

export interface SystemCardProps {
    title: string;
    icon: React.ComponentType<{ className?: string }> | string;
    primaryMetric: SystemMetric;
    secondaryMetrics: SystemMetric[];
    isLoading?: boolean;
    className?: string;
    onCardClick?: () => void;
    testId?: string;
}

// SystemCard Component
const SystemCard: React.FC<SystemCardProps> = ({
    title,
    icon,
    primaryMetric,
    secondaryMetrics,
    isLoading = false,
    className,
    onCardClick,
    testId = 'system-card',
}) => {
    // Handle icon rendering
    const renderIcon = () => {
        if (typeof icon === 'string') {
            return (
                <div className="w-12 h-12 flex items-center justify-center text-2xl bg-gray-100 rounded-lg">{icon}</div>
            );
        } else {
            const IconComponent = icon;
            return (
                <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-lg">
                    <IconComponent className="w-6 h-6 text-gray-600" />
                </div>
            );
        }
    };

    // Loading skeleton
    if (isLoading) {
        return (
            <div
                className={cn('bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse', className)}
                data-testid={`${testId}-loading`}>
                <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                    <div className="h-5 bg-gray-200 rounded w-32"></div>
                </div>
                <div className="space-y-3">
                    <div className="flex justify-between items-center">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                        <div className="h-6 bg-gray-200 rounded w-8"></div>
                    </div>
                    {secondaryMetrics.map((_, index) => (
                        <div key={index} className="flex justify-between items-center">
                            <div className="h-4 bg-gray-200 rounded w-20"></div>
                            <div className="h-4 bg-gray-200 rounded w-6"></div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div
            className={cn(
                'bg-white rounded-lg shadow-sm border border-gray-200 p-4 transition-all duration-200',
                onCardClick && 'cursor-pointer hover:shadow-md hover:border-gray-300',
                className,
            )}
            onClick={onCardClick}
            data-testid={testId}
            role={onCardClick ? 'button' : undefined}
            tabIndex={onCardClick ? 0 : undefined}
            onKeyDown={(e) => {
                if (onCardClick && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault();
                    onCardClick();
                }
            }}>
            {/* Header Section */}
            <div className="flex items-center gap-3 mb-4">
                {renderIcon()}
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            </div>

            {/* Primary Metric */}
            <div className="mb-4">
                <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{primaryMetric.label}</span>
                    <span
                        className={cn(
                            'text-2xl font-bold',
                            primaryMetric.isAlert ? 'text-red-600' : 'text-gray-900',
                            primaryMetric.highlight && 'text-blue-600',
                        )}>
                        {primaryMetric.value}
                    </span>
                </div>
            </div>

            {/* Secondary Metrics */}
            {secondaryMetrics.length > 0 && (
                <div className="space-y-2">
                    {secondaryMetrics.map((metric, index) => (
                        <div key={index} className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">{metric.label}</span>
                            <div className="flex items-center gap-2">
                                <span
                                    className={cn(
                                        'text-sm font-medium',
                                        metric.isAlert ? 'text-red-600' : 'text-gray-900',
                                        metric.highlight && 'text-blue-600',
                                    )}>
                                    {metric.value}
                                </span>
                                {/* Placeholder for AlertBadge - will be implemented later */}
                                {metric.isAlert && (
                                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default SystemCard;
